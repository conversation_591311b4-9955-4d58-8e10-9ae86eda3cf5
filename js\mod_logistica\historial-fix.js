/**
 * historial-fix.js
 * Solución específica para arreglar los estilos y etiquetas 
 * de las tarjetas de historial en el módulo de logística
 */

(function() {
    console.log('🔄 Inicializando historial-fix.js');
    
    // Función para modificar el HTML y agregar etiquetas "Movimiento:", "Desde:", "Hacia:"
    function fixHistorialLabels() {
        console.log('🔍 Buscando y arreglando etiquetas en historial');
        const webHistorial = document.getElementById('webHistorial');
        
        if (!webHistorial) {
            console.warn('⚠️ No se encontró el elemento webHistorial');
            return;
        }
        
        // Modificar párrafos con iconos específicos para agregar etiquetas
        const paragraphs = webHistorial.querySelectorAll('p');
        paragraphs.forEach(p => {
            const icon = p.querySelector('i');
            if (!icon) return;
            
            let label = '';
            
            // Determinar qué etiqueta agregar según el tipo de icono
            if (icon.classList.contains('bi-box') || icon.classList.contains('bi-arrow-left-right')) {
                if (!p.innerHTML.includes('Movimiento:')) {
                    label = 'Movimiento: ';
                }
            } else if (icon.classList.contains('bi-geo-alt') || icon.classList.contains('bi-geo-alt-fill')) {
                if (!p.innerHTML.includes('Desde:')) {
                    label = 'Desde: ';
                }
            } else if (icon.classList.contains('bi-heart')) {
                if (!p.innerHTML.includes('Hacia:')) {
                    label = 'Hacia: ';
                }
            }
            
            // Si se debe agregar una etiqueta, insertar después del icono
            if (label) {
                // Obtener el nodo de texto actual
                const textNodes = Array.from(p.childNodes).filter(node => 
                    node.nodeType === Node.TEXT_NODE || 
                    (node.nodeType === Node.ELEMENT_NODE && node.tagName !== 'I')
                );
                
                if (textNodes.length > 0) {
                    // Crear un span para la etiqueta con formato en negrita
                    const labelSpan = document.createElement('span');
                    labelSpan.textContent = label;
                    labelSpan.style.fontWeight = '600';
                    labelSpan.style.marginRight = '4px';
                    
                    // Insertar el span antes del primer nodo de texto
                    p.insertBefore(labelSpan, textNodes[0]);
                }
            }
        });
        
        console.log('✅ Etiquetas del historial arregladas correctamente');
    }
    
    // Función para agregar colores y estilos específicos a los iconos
    function fixHistorialIcons() {
        console.log('🔍 Ajustando estilos de iconos en historial');
        const webHistorial = document.getElementById('webHistorial');
        
        if (!webHistorial) {
            console.warn('⚠️ No se encontró el elemento webHistorial');
            return;
        }
        
        // Ajustar estilos de iconos y sus contenedores
        const icons = webHistorial.querySelectorAll('i.bi');
        icons.forEach(icon => {
            // Asegurar que el padre tiene display flex y align-items center
            if (icon.parentElement) {
                icon.parentElement.style.display = 'flex';
                icon.parentElement.style.alignItems = 'center';
                icon.parentElement.style.marginBottom = '8px';
            }
            
            // Asegurar estilos consistentes para el icono
            icon.style.width = '24px';
            icon.style.marginRight = '10px';
            icon.style.textAlign = 'center';
            icon.style.flexShrink = '0';
            
            // Aplicar colores específicos según el tipo de icono
            if (icon.classList.contains('bi-calendar')) {
                icon.style.color = 'white';
            } else if (icon.classList.contains('bi-box') || icon.classList.contains('bi-arrow-left-right')) {
                icon.style.color = '#00b8d4';
            } else if (icon.classList.contains('bi-geo-alt') || icon.classList.contains('bi-geo-alt-fill')) {
                icon.style.color = '#f44336';
            } else if (icon.classList.contains('bi-heart')) {
                icon.style.color = '#aa00ff';
            } else if (icon.classList.contains('bi-chat') || 
                     icon.classList.contains('bi-chat-text') || 
                     icon.classList.contains('bi-chat-square-text') ||
                     icon.classList.contains('bi-chat-dots')) {
                icon.style.color = '#ff9800';
            } else if (icon.classList.contains('bi-file') || 
                      icon.classList.contains('bi-download') ||
                      icon.classList.contains('bi-file-earmark-arrow-down')) {
                icon.style.color = '#26a69a';
            }
        });
        
        console.log('✅ Iconos del historial ajustados correctamente');
    }
    
    // Función para mejorar el diseño del contenedor principal
    function fixHistorialContainer() {
        console.log('🔍 Ajustando estilos del contenedor de historial');
        const webHistorial = document.getElementById('webHistorial');
        
        if (!webHistorial) {
            console.warn('⚠️ No se encontró el elemento webHistorial');
            return;
        }
        
        // Aplicar estilos al contenedor principal
        webHistorial.style.backgroundColor = 'rgba(30, 39, 70, 0.95)';
        webHistorial.style.padding = '12px';
        webHistorial.style.borderRadius = '8px';
        webHistorial.style.border = '1px solid rgba(0, 225, 253, 0.3)';
        webHistorial.style.maxHeight = '350px';
        webHistorial.style.overflowY = 'auto';
        webHistorial.style.color = 'white';
        webHistorial.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.2)';
        
        // Aplicar estilos a las tarjetas
        const cardItems = webHistorial.querySelectorAll('.card-item');
        cardItems.forEach(card => {
            card.style.backgroundColor = 'rgb(25, 33, 60)';
            card.style.border = '1px solid rgba(0, 225, 253, 0.3)';
            card.style.borderRadius = '6px';
            card.style.padding = '12px';
            card.style.marginBottom = '15px';
            card.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
        });
        
        // Aplicar estilos a las fechas
        const dates = webHistorial.querySelectorAll('.card-date');
        dates.forEach(date => {
            date.style.backgroundColor = '#0066b8';
            date.style.color = 'white';
            date.style.borderRadius = '4px';
            date.style.padding = '4px 10px';
            date.style.marginBottom = '8px';
            date.style.display = 'inline-flex';
            date.style.alignItems = 'center';
            date.style.fontSize = '13px';
            date.style.fontWeight = '500';
        });
        
        console.log('✅ Contenedor de historial ajustado correctamente');
    }
    
    // Función principal que ejecuta todas las correcciones
    function applyHistorialFixes() {
        console.log('🚀 Aplicando correcciones al historial');
        
        // Aplicar cada una de las correcciones
        fixHistorialContainer();
        fixHistorialIcons();
        fixHistorialLabels();
        
        console.log('✅ Todas las correcciones aplicadas correctamente');
    }
    
    // Configurar observador de mutaciones para detectar cuando se agrega el historial
    function setupMutationObserver() {
        console.log('🔍 Configurando observador de mutaciones');
        
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // Verificar si se ha agregado el elemento webHistorial
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        
                        // Verificar si es un elemento DOM
                        if (node.nodeType === 1) {
                            // Buscar el elemento webHistorial
                            if (node.id === 'webHistorial' || node.querySelector('#webHistorial')) {
                                console.log('🔍 Se ha detectado la adición del elemento webHistorial');
                                setTimeout(applyHistorialFixes, 100);
                                return;
                            }
                            
                            // También buscar cambios en el contenido del historial
                            const webHistorial = document.getElementById('webHistorial');
                            if (webHistorial && node.contains(webHistorial)) {
                                console.log('🔍 Se ha detectado un cambio en el contenido del historial');
                                setTimeout(applyHistorialFixes, 100);
                                return;
                            }
                        }
                    }
                }
            });
        });
        
        // Iniciar observación del documento completo
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        console.log('✅ Observador de mutaciones configurado');
    }
    
    // Configurar eventos para offcanvas
    function setupOffcanvasEvents() {
        document.addEventListener('shown.bs.offcanvas', function(event) {
            if (event.target.id === 'offcanvasHistorial' || event.target.id === 'canvaHistorial') {
                console.log('🔍 Offcanvas de historial mostrado, aplicando correcciones');
                setTimeout(applyHistorialFixes, 100);
            }
        });
    }
    
    // Función de inicialización
    function init() {
        console.log('🚀 Inicializando sistema de corrección de historial');
        
        // Configurar el observador de mutaciones
        setupMutationObserver();
        
        // Configurar eventos para offcanvas
        setupOffcanvasEvents();
        
        // Sobrescribir función original si existe
        if (typeof window.cargarHistorial === 'function') {
            console.log('🔄 Sobrescribiendo la función cargarHistorial original');
            const originalCargarHistorial = window.cargarHistorial;
            
            window.cargarHistorial = function(serie) {
                console.log('📋 Versión mejorada de cargarHistorial llamada para serie:', serie);
                
                // Llamar a la función original
                originalCargarHistorial(serie);
                
                // Aplicar correcciones después de un pequeño delay
                setTimeout(applyHistorialFixes, 300);
            };
            
            console.log('✅ Función cargarHistorial sobrescrita exitosamente');
        }
        
        console.log('✅ Sistema de corrección de historial inicializado correctamente');
    }
    
    // DESHABILITADO: Los estilos ahora están 100% en CSS
    // Este script interfería con el CSS unificado  
    console.log('⚠️ historial-fix.js DESHABILITADO - usando CSS unificado');
    
    // NO ejecutar inicialización
    // if (document.readyState === 'loading') {
    //     document.addEventListener('DOMContentLoaded', init);
    // } else {
    //     init();
    // }
    
    // Exponer función global para permitir activación manual
    window.applyHistorialFixes = applyHistorialFixes;
})();