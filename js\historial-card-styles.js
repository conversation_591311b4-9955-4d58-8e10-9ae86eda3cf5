/**
 * historial-card-styles.js
 * Script para inyectar estilos CSS directamente en el HTML para los casos
 * donde los archivos CSS no se cargan correctamente o hay problemas de caché.
 */

(function() {
    // Función para inyectar los estilos directamente en el documento
    function injectStyles() {
        // Verificar si ya se han inyectado los estilos
        if (document.getElementById('historial-card-styles')) {
            console.log('✅ Estilos de tarjetas de historial ya inyectados');
            return;
        }

        console.log('🔧 Inyectando estilos de tarjetas de historial directamente');
        
        // Crear el elemento style
        const styleElement = document.createElement('style');
        styleElement.id = 'historial-card-styles';
        
        // Definir los estilos CSS inline - Adaptados a la captura de pantalla
        styleElement.textContent = `
            /* Ajustes para el offcanvas de historial */
            #offcanvasHistorial .offcanvas-body,
            #canvaHistorial .offcanvas-body {
                background-color: #1e2746;
                padding: 15px !important;
            }

            /* Ajustes para el contenedor de historial */
            #webHistorial {
                padding: 12px !important;
                border-radius: 8px !important;
                border: 1px solid rgba(0, 225, 253, 0.3) !important;
                background-color: rgba(30, 39, 70, 0.95) !important;
                max-height: 350px !important;
                overflow-y: auto !important;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
                color: white !important;
            }

            /* Scrollbar personalizada */
            #webHistorial::-webkit-scrollbar {
                width: 5px !important;
            }

            #webHistorial::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.05) !important;
            }

            #webHistorial::-webkit-scrollbar-thumb {
                background: rgba(0, 225, 253, 0.5) !important;
                border-radius: 3px !important;
            }

            /* Selector específico para las tarjetas - Exactamente como en la captura */
            #webHistorial .card-item {
                background: rgb(25, 33, 60) !important;
                border: 1px solid rgba(0, 225, 253, 0.3) !important;
                border-radius: 6px !important;
                padding: 12px !important;
                margin-bottom: 15px !important;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
            }

            /* Selector específico para el contenido de las tarjetas */
            #webHistorial .card-content {
                display: flex !important;
                flex-direction: column !important;
                gap: 8px !important;
            }

            /* Estilos para la fecha/hora en la parte superior - Como en la captura */
            #webHistorial .card-date,
            #webHistorial span.card-date {
                background-color: #0066b8 !important;
                color: white !important;
                border-radius: 4px !important;
                padding: 4px 10px !important;
                margin-bottom: 8px !important;
                display: inline-flex !important;
                align-items: center !important;
                font-size: 13px !important;
                font-weight: 500 !important;
                max-width: fit-content !important;
            }

            /* Estilos generales para alinear iconos y texto - Como en la captura */
            #webHistorial .card-info,
            p.card-info {
                display: flex !important;
                align-items: center !important;
                margin-bottom: 8px !important;
                font-size: 14px !important;
                line-height: 1.5 !important;
                padding: 0 !important;
                margin-left: 0 !important;
                margin-right: 0 !important;
            }

            /* Ajustes específicos para los iconos - Como en la captura */
            #webHistorial i.bi {
                width: 24px !important;
                margin-right: 10px !important;
                text-align: center !important;
                font-size: 16px !important;
                flex-shrink: 0 !important;
            }

            /* Colores específicos para cada tipo de icono - Adaptados a la captura */
            #webHistorial i.bi-calendar,
            #webHistorial i[class*="calendar"] {
                color: white !important;
            }

            #webHistorial i.bi-box, 
            #webHistorial i.bi-arrow-left-right, 
            #webHistorial i[class*="box"] {
                color: #00b8d4 !important;
            }

            #webHistorial i.bi-geo-alt, 
            #webHistorial i.bi-geo-alt-fill, 
            #webHistorial i[class*="geo"] {
                color: #f44336 !important;
            }

            #webHistorial i.bi-heart, 
            #webHistorial i[class*="heart"], 
            #webHistorial i[class*="heart-fill"] {
                color: #aa00ff !important;
            }

            #webHistorial i.bi-chat, 
            #webHistorial i.bi-chat-text, 
            #webHistorial i.bi-chat-square-text, 
            #webHistorial i.bi-chat-dots,
            #webHistorial i[class*="chat"] {
                color: #ff9800 !important;
            }

            #webHistorial i.bi-file, 
            #webHistorial i.bi-download,
            #webHistorial i.bi-file-earmark-arrow-down,
            #webHistorial i[class*="file"], 
            #webHistorial i[class*="download"] {
                color: #26a69a !important;
            }

            /* Texto para coincidencia exacta con captura - Etiquetas */
            #webHistorial p:has(i.bi-box)::before {
                content: "Movimiento: " !important;
                font-weight: 600 !important;
                margin-right: 4px !important;
            }

            #webHistorial p:has(i.bi-geo-alt)::before {
                content: "Desde: " !important;
                font-weight: 600 !important;
                margin-right: 4px !important;
            }

            #webHistorial p:has(i.bi-heart)::before {
                content: "Hacia: " !important;
                font-weight: 600 !important;
                margin-right: 4px !important;
            }

            /* Asegurar que el primer div (fecha) sea el único con ese background */
            #webHistorial > div > div:first-of-type {
                display: inline-flex !important;
                align-items: center !important;
                border-radius: 4px !important;
                padding: 4px 10px !important;
                margin-bottom: 8px !important;
                font-size: 13px !important;
                font-weight: 500 !important;
            }

            /* Estilos específicos para elementos generados por el script historial_loader.js */
            #webHistorial .movimiento-info,
            #webHistorial .desde-info,
            #webHistorial .hacia-info,
            #webHistorial .motivo-info,
            #webHistorial .obs-info,
            #webHistorial .archivo-info {
                display: flex !important;
                align-items: center !important;
                margin-bottom: 8px !important;
                font-size: 14px !important;
            }

            #webHistorial .movimiento-info i,
            #webHistorial .desde-info i,
            #webHistorial .hacia-info i,
            #webHistorial .motivo-info i,
            #webHistorial .obs-info i,
            #webHistorial .archivo-info i {
                width: 24px !important;
                margin-right: 10px !important;
                text-align: center !important;
                font-size: 16px !important;
                flex-shrink: 0 !important;
            }

            /* Estilos para los campos de entrada */
            #serieHistorial, #precioHistorial {
                background-color: rgba(0, 0, 0, 0.2) !important;
                border: 1px solid rgba(255, 255, 255, 0.1) !important;
                color: white !important;
            }

            /* Estilo para links dentro del historial */
            #webHistorial a {
                color: #4fc3f7 !important;
                text-decoration: none !important;
                transition: color 0.2s !important;
            }

            #webHistorial a:hover {
                color: #81d4fa !important;
                text-decoration: underline !important;
            }
        `;
        
        // Añadir el elemento style al head
        document.head.appendChild(styleElement);
        console.log('✅ Estilos de tarjetas de historial inyectados exitosamente');
    }

    // Función para sobrescribir el método cargarHistorial
    function overrideHistorialLoader() {
        // Guardar la referencia original a la función cargarHistorial si existe
        if (typeof window.cargarHistorial === 'function') {
            console.log('🔄 Sobrescribiendo la función cargarHistorial original');
            const originalCargarHistorial = window.cargarHistorial;
            
            // Sobrescribir con nuestra versión mejorada
            window.cargarHistorial = function(serie) {
                console.log('📋 Versión mejorada de cargarHistorial llamada para serie:', serie);
                
                // Llamar a la función original
                originalCargarHistorial(serie);
                
                // Añadir un pequeño delay para asegurar que el historial se ha cargado
                setTimeout(function() {
                    // Aplicar estilos adicionales específicamente para el contenido generado
                    const webHistorial = document.getElementById('webHistorial');
                    if (webHistorial) {
                        console.log('🔍 Aplicando mejoras de estilo a los elementos cargados');
                        
                        // Recorrer todos los elementos i (iconos) dentro de webHistorial
                        const icons = webHistorial.querySelectorAll('i.bi');
                        icons.forEach(icon => {
                            // Asegurar que el padre tiene display flex y align-items center
                            if (icon.parentElement) {
                                icon.parentElement.style.display = 'flex';
                                icon.parentElement.style.alignItems = 'center';
                                
                                // Asegurar estilos consistentes para el icono
                                icon.style.width = '24px';
                                icon.style.marginRight = '10px';
                                icon.style.textAlign = 'center';
                                icon.style.flexShrink = '0';
                            }
                        });
                        
                        console.log('✅ Mejoras de estilo aplicadas exitosamente');
                    }
                }, 300); // Un retraso de 300ms debería ser suficiente
            };
            
            console.log('✅ Función cargarHistorial sobrescrita exitosamente');
        } else {
            console.log('⚠️ No se encontró la función cargarHistorial para sobrescribir');
        }

        // Hacer lo mismo con cargarHistorialDirecto si existe
        if (typeof window.cargarHistorialDirecto === 'function') {
            console.log('🔄 Sobrescribiendo la función cargarHistorialDirecto original');
            const originalCargarHistorialDirecto = window.cargarHistorialDirecto;
            
            // Sobrescribir con nuestra versión mejorada
            window.cargarHistorialDirecto = function(serie, id_orden) {
                console.log('📋 Versión mejorada de cargarHistorialDirecto llamada para serie:', serie);
                
                // Llamar a la función original
                const result = originalCargarHistorialDirecto(serie, id_orden);
                
                // Aplicar los mismos ajustes de estilo con un pequeño delay
                setTimeout(function() {
                    const webHistorial = document.getElementById('webHistorial');
                    if (webHistorial) {
                        console.log('🔍 Aplicando mejoras de estilo a los elementos cargados');
                        
                        const icons = webHistorial.querySelectorAll('i.bi');
                        icons.forEach(icon => {
                            if (icon.parentElement) {
                                icon.parentElement.style.display = 'flex';
                                icon.parentElement.style.alignItems = 'center';
                                icon.style.width = '24px';
                                icon.style.marginRight = '10px';
                                icon.style.textAlign = 'center';
                                icon.style.flexShrink = '0';
                            }
                        });
                        
                        console.log('✅ Mejoras de estilo aplicadas exitosamente');
                    }
                }, 300);
                
                return result;
            };
            
            console.log('✅ Función cargarHistorialDirecto sobrescrita exitosamente');
        } else {
            console.log('⚠️ No se encontró la función cargarHistorialDirecto para sobrescribir');
        }
    }

    // Función para observar cambios en el DOM y aplicar estilos a elementos dinámicos
    function setupMutationObserver() {
        // Crear un observador que detecte cambios en el DOM
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // Verificar si se ha agregado el elemento webHistorial
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        
                        // Verificar si es un elemento DOM
                        if (node.nodeType === 1) {
                            // Buscar el elemento webHistorial
                            if (node.id === 'webHistorial' || node.querySelector('#webHistorial')) {
                                console.log('🔍 Se ha detectado la adición del elemento webHistorial');
                                
                                // Aplicar estilos con un pequeño delay
                                setTimeout(function() {
                                    const webHistorial = document.getElementById('webHistorial');
                                    if (webHistorial) {
                                        const icons = webHistorial.querySelectorAll('i.bi');
                                        icons.forEach(icon => {
                                            if (icon.parentElement) {
                                                icon.parentElement.style.display = 'flex';
                                                icon.parentElement.style.alignItems = 'center';
                                                icon.style.width = '24px';
                                                icon.style.marginRight = '10px';
                                                icon.style.textAlign = 'center';
                                                icon.style.flexShrink = '0';
                                            }
                                        });
                                    }
                                }, 100);
                                
                                return;
                            }
                        }
                    }
                }
            });
        });
        
        // Iniciar observación del documento completo
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        console.log('✅ Observador de mutaciones configurado');
    }

    // Función principal de inicialización
    function init() {
        console.log('🚀 Inicializando sistema de estilos para tarjetas de historial');
        
        // Inyectar estilos CSS
        injectStyles();
        
        // Sobrescribir funciones de carga de historial
        overrideHistorialLoader();
        
        // Configurar observador de cambios en el DOM
        setupMutationObserver();
        
        // Configurar eventos para offcanvas
        document.addEventListener('shown.bs.offcanvas', function(event) {
            if (event.target.id === 'offcanvasHistorial' || event.target.id === 'canvaHistorial') {
                console.log('🔍 Offcanvas de historial mostrado, verificando estilos');
                
                // Pequeño delay para asegurar que todo está cargado
                setTimeout(function() {
                    const webHistorial = document.getElementById('webHistorial');
                    if (webHistorial) {
                        const icons = webHistorial.querySelectorAll('i.bi');
                        icons.forEach(icon => {
                            if (icon.parentElement) {
                                icon.parentElement.style.display = 'flex';
                                icon.parentElement.style.alignItems = 'center';
                                icon.style.width = '24px';
                                icon.style.marginRight = '10px';
                                icon.style.textAlign = 'center';
                                icon.style.flexShrink = '0';
                            }
                        });
                    }
                }, 200);
            }
        });
        
        console.log('✅ Sistema de estilos para tarjetas de historial inicializado correctamente');
    }

    // DESHABILITADO: Los estilos ahora están 100% en CSS
    // Este script interfería con el CSS unificado
    console.log('⚠️ historial-card-styles.js DESHABILITADO - usando CSS unificado');
    
    // NO ejecutar inicialización
    // if (document.readyState === 'loading') {
    //     document.addEventListener('DOMContentLoaded', init);
    // } else {
    //     init();
    // }

    // Exponer función global para permitir activación manual
    window.applyHistorialCardStyles = function() {
        console.log('🔧 Aplicando estilos de tarjetas de historial manualmente');
        injectStyles();
        
        const webHistorial = document.getElementById('webHistorial');
        if (webHistorial) {
            const icons = webHistorial.querySelectorAll('i.bi');
            icons.forEach(icon => {
                if (icon.parentElement) {
                    icon.parentElement.style.display = 'flex';
                    icon.parentElement.style.alignItems = 'center';
                    icon.style.width = '24px';
                    icon.style.marginRight = '10px';
                    icon.style.textAlign = 'center';
                    icon.style.flexShrink = '0';
                }
            });
        }
        
        console.log('✅ Estilos aplicados manualmente');
    };
})();